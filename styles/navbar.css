.nav-button {
  @apply relative px-3 py-2 text-gray-700 hover:text-gray-900 font-medium transition-all duration-300;
  @apply after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-purple-600;
  @apply after:left-0 after:-bottom-1 after:transition-all after:duration-300;
  @apply hover:after:w-full;
}

.auth-button {
  @apply px-4 py-2 rounded-lg font-medium transition-all duration-300;
  @apply bg-purple-600 text-white hover:bg-purple-700;
  @apply shadow-md hover:shadow-lg;
}

.nav-link {
  @apply relative px-3 py-2 text-gray-700 hover:text-gray-900;
  @apply after:content-[''] after:absolute after:w-0 after:h-0.5 after:bg-purple-600;
  @apply after:left-0 after:-bottom-1 after:transition-all after:duration-300;
  @apply hover:after:w-full;
}

/* Mobile menu animation */
.mobile-menu-enter {
  opacity: 0;
  transform: scale(0.95);
}

.mobile-menu-enter-active {
  opacity: 1;
  transform: scale(1);
  transition: opacity 200ms ease-out, transform 200ms ease-out;
}

.mobile-menu-exit {
  opacity: 1;
  transform: scale(1);
}

.mobile-menu-exit-active {
  opacity: 0;
  transform: scale(0.95);
  transition: opacity 200ms ease-in, transform 200ms ease-in;
}

/* Scroll animation */
.nav-scrolled {
  @apply shadow-lg bg-white/80 backdrop-blur-md;
}

/* Logo animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.logo-spin:hover {
  animation: spin 1s ease-in-out;
}
