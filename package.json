{"private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001"}, "dependencies": {"@clerk/types": "^4.35.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/nodemailer": "^6.4.17", "bcryptjs": "^2.4.3", "dotenv": "^16.4.5", "framer-motion": "^11.11.17", "jsonwebtoken": "^9.0.2", "minio": "^8.0.2", "mongodb": "^6.5.0", "mongoose": "^8.8.2", "next": "latest", "next-themes": "^0.4.3", "nodemailer": "^6.9.16", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.4.1", "react-icons": "^5.3.0", "react-responsive-carousel": "^3.2.23"}, "devDependencies": {"@types/minio": "^7.1.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5.7.2"}}